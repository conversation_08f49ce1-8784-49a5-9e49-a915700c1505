package TestController

import (
	"net/http"
	
	"github.com/gin-gonic/gin"
)

// TestController Controller
type TestController struct {
	// TODO: Add service dependencies
}

// RegisterTestController Register routes
func RegisterTestController(r *gin.RouterGroup) {
	controller := &%!s(MISSING){}
	
	r.GET("/user_list", controller.UserList)
	r.GET("/get_user", controller.GetUser)
	r.POST("/save", controller.Save)
	r.PUT("/update", controller.Update)
	r.DELETE("/delete", controller.Delete)
}

// UserList Handle request
func (c *TestController) UserList(ctx *gin.Context) {
	// TODO: Implement controller logic
	ctx.JSON(http.StatusOK, gin.H{"message": "Success"})
}

// GetUser Handle request
func (c *TestController) GetUser(ctx *gin.Context) {
	// TODO: Implement controller logic
	ctx.JSON(http.StatusOK, gin.H{"message": "Success"})
}

// Save Handle request
func (c *TestController) Save(ctx *gin.Context) {
	// TODO: Implement controller logic
	ctx.JSON(http.StatusOK, gin.H{"message": "Success"})
}

// Update Handle request
func (c *TestController) Update(ctx *gin.Context) {
	// TODO: Implement controller logic
	ctx.JSON(http.StatusOK, gin.H{"message": "Success"})
}

// Delete Handle request
func (c *TestController) Delete(ctx *gin.Context) {
	// TODO: Implement controller logic
	ctx.JSON(http.StatusOK, gin.H{"message": "Success"})
}

