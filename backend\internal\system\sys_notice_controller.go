package system

import (
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/ruoyi/backend/internal/api/controller"
	"github.com/ruoyi/backend/internal/domain"
	"github.com/ruoyi/backend/internal/service"
	"go.uber.org/zap"
)

// SysNoticeController 公告信息控制器
type SysNoticeController struct {
	controller.BaseController
	noticeService service.ISysNoticeService
}

// NewSysNoticeController 创建公告控制器
func NewSysNoticeController(
	logger *zap.Logger,
	noticeService service.ISysNoticeService,
) *SysNoticeController {
	return &SysNoticeController{
		BaseController: controller.BaseController{
			Logger: logger,
		},
		noticeService: noticeService,
	}
}

// RegisterRoutes 注册路由
func (c *SysNoticeController) RegisterRoutes(r *gin.RouterGroup) {
	r.GET("/list", c.List)
	r.GET("/:noticeId", c.GetInfo)
	r.POST("", c.Add)
	r.PUT("", c.Edit)
	r.DELETE("/:noticeIds", c.Remove)
}

// List 获取通知公告列表
func (c *SysNoticeController) List(ctx *gin.Context) {
	c.StartPage(ctx)

	// 构建查询条件
	notice := &domain.SysNotice{}
	// 从请求中绑定参数
	// TODO: 实现参数绑定

	// 调用服务获取通知公告列表
	list := c.noticeService.SelectNoticeList(*notice)

	// 返回结果
	ctx.JSON(http.StatusOK, c.GetDataTable(ctx, list, int64(len(list))))
}

// GetInfo 根据通知公告编号获取详细信息
func (c *SysNoticeController) GetInfo(ctx *gin.Context) {
	noticeIdStr := ctx.Param("noticeId")
	noticeId, err := strconv.ParseInt(noticeIdStr, 10, 64)
	if err != nil {
		c.ErrorWithMessage(ctx, "公告ID格式错误")
		return
	}

	// 获取通知公告信息
	notice := c.noticeService.SelectNoticeById(noticeId)
	c.SuccessWithData(ctx, notice)
}

// Add 新增通知公告
func (c *SysNoticeController) Add(ctx *gin.Context) {
	var notice domain.SysNotice
	if err := ctx.ShouldBindJSON(&notice); err != nil {
		c.ErrorWithMessage(ctx, "参数错误")
		return
	}

	// 设置创建者
	notice.CreateBy = c.GetUsername(ctx)

	// 新增通知公告
	rows := c.noticeService.InsertNotice(notice)
	c.ToAjax(ctx, int64(rows))
}

// Edit 修改通知公告
func (c *SysNoticeController) Edit(ctx *gin.Context) {
	var notice domain.SysNotice
	if err := ctx.ShouldBindJSON(&notice); err != nil {
		c.ErrorWithMessage(ctx, "参数错误")
		return
	}

	// 设置更新者
	notice.UpdateBy = c.GetUsername(ctx)

	// 更新通知公告
	rows := c.noticeService.UpdateNotice(notice)
	c.ToAjax(ctx, int64(rows))
}

// Remove 删除通知公告
func (c *SysNoticeController) Remove(ctx *gin.Context) {
	noticeIdsStr := ctx.Param("noticeIds")
	noticeIds := strings.Split(noticeIdsStr, ",")

	// 将字符串ID转换为int64数组
	ids := make([]int64, 0, len(noticeIds))
	for _, idStr := range noticeIds {
		id, err := strconv.ParseInt(idStr, 10, 64)
		if err != nil {
			continue
		}
		ids = append(ids, id)
	}

	// 删除通知公告
	rows := c.noticeService.DeleteNoticeByIds(ids)
	c.ToAjax(ctx, int64(rows))
}
