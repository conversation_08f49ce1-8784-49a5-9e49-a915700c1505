package config

import (
	"time"
)

// JWTConfig JWT配置
type JWTConfig struct {
	// Secret JWT密钥
	Secret string `mapstructure:"secret" json:"secret" yaml:"secret"`

	// Expire JWT过期时间（秒）
	Expire int `mapstructure:"expire" json:"expire" yaml:"expire"`

	// Issuer JWT签发者
	Issuer string `mapstructure:"issuer" json:"issuer" yaml:"issuer"`

	// Header JWT请求头
	Header string `mapstructure:"header" json:"header" yaml:"header"`

	// TokenPrefix JWT令牌前缀
	TokenPrefix string `mapstructure:"token_prefix" json:"token_prefix" yaml:"token_prefix"`

	// RefreshExpire 刷新令牌过期时间（秒）
	RefreshExpire int `mapstructure:"refresh_expire" json:"refresh_expire" yaml:"refresh_expire"`
}

// NewJWTConfig 创建JWT配置
func NewJWTConfig() *JWTConfig {
	return &JWTConfig{
		Secret:        "abcdefghijklmnopqrstuvwxyz",
		Expire:        86400,
		Issuer:        "ruoyi-go",
		Header:        "Authorization",
		TokenPrefix:   "Bearer ",
		RefreshExpire: 604800,
	}
}

// GetSecret 获取JWT密钥
func (c *JWTConfig) GetSecret() string {
	return c.Secret
}

// GetExpire 获取JWT过期时间
func (c *JWTConfig) GetExpire() int {
	return c.Expire
}

// GetExpireDuration 获取JWT过期时间（Duration）
func (c *JWTConfig) GetExpireDuration() time.Duration {
	return time.Duration(c.Expire) * time.Second
}

// GetIssuer 获取JWT签发者
func (c *JWTConfig) GetIssuer() string {
	return c.Issuer
}

// GetHeader 获取JWT请求头
func (c *JWTConfig) GetHeader() string {
	return c.Header
}

// GetTokenPrefix 获取JWT令牌前缀
func (c *JWTConfig) GetTokenPrefix() string {
	return c.TokenPrefix
}

// GetRefreshExpire 获取刷新令牌过期时间
func (c *JWTConfig) GetRefreshExpire() int {
	return c.RefreshExpire
}

// GetRefreshExpireDuration 获取刷新令牌过期时间（Duration）
func (c *JWTConfig) GetRefreshExpireDuration() time.Duration {
	return time.Duration(c.RefreshExpire) * time.Second
}
