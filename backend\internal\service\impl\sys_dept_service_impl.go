package impl

import (
	"strconv"
	"strings"

	"github.com/ruoyi/backend/internal/domain"
	"github.com/ruoyi/backend/internal/repository"
	"github.com/ruoyi/backend/internal/service"
	"github.com/ruoyi/backend/internal/vo"
)

// SysDeptServiceImpl 部门服务实现
type SysDeptServiceImpl struct {
	// 部门仓储
	DeptRepository repository.DeptRepository
	// 角色仓储
	RoleRepository repository.RoleRepository
}

// NewSysDeptService 创建部门服务
func NewSysDeptService(deptRepository repository.DeptRepository, roleRepository repository.RoleRepository) service.ISysDeptService {
	return &SysDeptServiceImpl{
		DeptRepository: deptRepository,
		RoleRepository: roleRepository,
	}
}

// SelectDeptList 查询部门管理数据
func (s *SysDeptServiceImpl) SelectDeptList(dept domain.SysDept) []domain.SysDept {
	depts, err := s.DeptRepository.SelectDeptList(&dept)
	if err != nil {
		return []domain.SysDept{}
	}
	return depts
}

// SelectDeptTreeList 查询部门树结构信息
func (s *SysDeptServiceImpl) SelectDeptTreeList(dept domain.SysDept) []vo.TreeSelect {
	depts := s.SelectDeptList(dept)
	return s.BuildDeptTreeSelect(depts)
}

// BuildDeptTree 构建前端所需要树结构
func (s *SysDeptServiceImpl) BuildDeptTree(depts []domain.SysDept) []domain.SysDept {
	return s.buildDeptTree(depts, 0)
}

// BuildDeptTreeSelect 构建前端所需要下拉树结构
func (s *SysDeptServiceImpl) BuildDeptTreeSelect(depts []domain.SysDept) []vo.TreeSelect {
	deptTrees := s.BuildDeptTree(depts)
	return s.buildDeptTreeSelect(deptTrees)
}

// SelectDeptListByRoleId 根据角色ID查询部门树信息
func (s *SysDeptServiceImpl) SelectDeptListByRoleId(roleId int64) []int64 {
	deptIds, err := s.DeptRepository.SelectDeptListByRoleId(roleId)
	if err != nil {
		return []int64{}
	}
	return deptIds
}

// SelectDeptById 根据部门ID查询信息
func (s *SysDeptServiceImpl) SelectDeptById(deptId int64) domain.SysDept {
	dept, err := s.DeptRepository.SelectDeptById(deptId)
	if err != nil {
		return domain.SysDept{}
	}
	return *dept
}

// SelectNormalChildrenDeptById 根据ID查询所有子部门（正常状态）
func (s *SysDeptServiceImpl) SelectNormalChildrenDeptById(deptId int64) int {
	count, err := s.DeptRepository.SelectNormalChildrenDeptById(deptId)
	if err != nil {
		return 0
	}
	return count
}

// HasChildByDeptId 是否存在部门子节点
func (s *SysDeptServiceImpl) HasChildByDeptId(deptId int64) bool {
	count, err := s.DeptRepository.HasChildByDeptId(deptId)
	if err != nil {
		return false
	}
	return count > 0
}

// CheckDeptExistUser 查询部门是否存在用户
func (s *SysDeptServiceImpl) CheckDeptExistUser(deptId int64) bool {
	count, err := s.DeptRepository.CheckDeptExistUser(deptId)
	if err != nil {
		return false
	}
	return count > 0
}

// CheckDeptNameUnique 校验部门名称是否唯一
func (s *SysDeptServiceImpl) CheckDeptNameUnique(dept domain.SysDept) bool {
	existDept, err := s.DeptRepository.CheckDeptNameUnique(dept.DeptName, dept.ParentId)
	if err != nil {
		return false
	}

	if existDept != nil && existDept.DeptId != dept.DeptId {
		return false
	}
	return true
}

// CheckDeptDataScope 校验部门是否有数据权限
func (s *SysDeptServiceImpl) CheckDeptDataScope(deptId int64) {
	// TODO: 实现数据权限校验
}

// InsertDept 新增保存部门信息
func (s *SysDeptServiceImpl) InsertDept(dept domain.SysDept) int {
	parentDept, err := s.DeptRepository.SelectDeptById(dept.ParentId)
	if err != nil {
		return 0
	}

	// 如果父节点不为正常状态，则不允许新增子节点
	if parentDept != nil && parentDept.Status != "0" {
		return 0
	}

	// 设置祖级列表
	if parentDept != nil {
		dept.Ancestors = parentDept.Ancestors + "," + parentDept.DeptId
	} else {
		dept.Ancestors = "0"
	}

	err = s.DeptRepository.InsertDept(&dept)
	if err != nil {
		return 0
	}
	return 1
}

// UpdateDept 修改保存部门信息
func (s *SysDeptServiceImpl) UpdateDept(dept domain.SysDept) int {
	oldDept, err := s.DeptRepository.SelectDeptById(dept.DeptId)
	if err != nil || oldDept == nil {
		return 0
	}

	// 如果父节点不为正常状态，则不允许更新子节点
	newParentDept, err := s.DeptRepository.SelectDeptById(dept.ParentId)
	if err != nil || (newParentDept != nil && newParentDept.Status != "0") {
		return 0
	}

	// 不能将自己设为自己的父节点
	if dept.ParentId == dept.DeptId {
		return 0
	}

	// 更新子元素关系
	oldAncestors := oldDept.Ancestors
	newAncestors := ""
	if newParentDept != nil {
		newAncestors = newParentDept.Ancestors + "," + newParentDept.DeptId
	} else {
		newAncestors = "0"
	}
	dept.Ancestors = newAncestors

	// 更新部门信息
	err = s.DeptRepository.UpdateDept(&dept)
	if err != nil {
		return 0
	}

	// 如果更新了父节点，则更新子节点的祖级列表
	if oldDept.ParentId != dept.ParentId {
		s.updateDeptChildren(dept.DeptId, newAncestors, oldAncestors)
	}

	// 如果该部门是启用状态，则启用该部门的所有上级部门
	if dept.Status == "0" {
		s.updateParentDeptStatusNormal(dept)
	}

	return 1
}

// DeleteDeptById 删除部门管理信息
func (s *SysDeptServiceImpl) DeleteDeptById(deptId int64) int {
	err := s.DeptRepository.DeleteDeptById(deptId)
	if err != nil {
		return 0
	}
	return 1
}

// updateParentDeptStatusNormal 修改父部门状态
func (s *SysDeptServiceImpl) updateParentDeptStatusNormal(dept domain.SysDept) {
	if dept.Ancestors != "" {
		ancestors := strings.Split(dept.Ancestors, ",")
		for _, ancestorId := range ancestors {
			if ancestorId != "0" {
				id, err := strconv.ParseInt(ancestorId, 10, 64)
				if err != nil {
					continue
				}

				ancestor, err := s.DeptRepository.SelectDeptById(id)
				if err != nil || ancestor == nil {
					continue
				}

				// 如果父部门不是正常状态，则更新为正常状态
				if ancestor.Status != "0" {
					ancestor.Status = "0"
					s.DeptRepository.UpdateDept(ancestor)
				}
			}
		}
	}
}

// updateDeptChildren 修改子元素关系
func (s *SysDeptServiceImpl) updateDeptChildren(deptId int64, newAncestors, oldAncestors string) {
	children, err := s.DeptRepository.SelectChildrenDeptById(deptId)
	if err != nil || len(children) == 0 {
		return
	}

	for _, child := range children {
		child.Ancestors = strings.Replace(child.Ancestors, oldAncestors, newAncestors, 1)
		s.DeptRepository.UpdateDept(&child)
	}
}

// buildDeptTree 构建部门树
func (s *SysDeptServiceImpl) buildDeptTree(depts []domain.SysDept, parentId int64) []domain.SysDept {
	deptTree := make([]domain.SysDept, 0)
	for _, dept := range depts {
		if dept.ParentId == parentId {
			s.recursionFn(depts, &dept)
			deptTree = append(deptTree, dept)
		}
	}
	return deptTree
}

// recursionFn 递归列表
func (s *SysDeptServiceImpl) recursionFn(list []domain.SysDept, dept *domain.SysDept) {
	// 得到子节点列表
	childList := s.getChildList(list, *dept)
	dept.Children = childList
	for i := 0; i < len(childList); i++ {
		child := childList[i]
		// 判断是否有子节点
		if s.hasChild(list, child) {
			s.recursionFn(list, &childList[i])
		}
	}
}

// getChildList 得到子节点列表
func (s *SysDeptServiceImpl) getChildList(list []domain.SysDept, dept domain.SysDept) []domain.SysDept {
	childList := make([]domain.SysDept, 0)
	for _, child := range list {
		if child.ParentId == dept.DeptId {
			childList = append(childList, child)
		}
	}
	return childList
}

// hasChild 判断是否有子节点
func (s *SysDeptServiceImpl) hasChild(list []domain.SysDept, dept domain.SysDept) bool {
	return len(s.getChildList(list, dept)) > 0
}

// buildDeptTreeSelect 构建下拉树结构
func (s *SysDeptServiceImpl) buildDeptTreeSelect(depts []domain.SysDept) []vo.TreeSelect {
	treeSelects := make([]vo.TreeSelect, 0)
	for _, dept := range depts {
		treeSelect := vo.TreeSelect{
			ID:    dept.DeptId,
			Label: dept.DeptName,
		}
		if len(dept.Children) > 0 {
			treeSelect.Children = s.buildDeptTreeSelect(dept.Children)
		}
		treeSelects = append(treeSelects, treeSelect)
	}
	return treeSelects
}
