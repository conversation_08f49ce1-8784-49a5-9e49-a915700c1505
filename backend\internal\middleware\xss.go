package middleware

import (
	"bytes"
	"encoding/json"
	"html"
	"io/ioutil"
	"strings"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// XssMiddleware XSS过滤中间件
func XssMiddleware(logger *zap.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取内容类型
		contentType := c.Get<PERSON>eader("Content-Type")

		// 判断是否需要过滤
		if !shouldFilter(c.Request.URL.Path) {
			c.Next()
			return
		}

		// 处理JSON请求
		if strings.Contains(contentType, "application/json") {
			// 读取请求体
			body, err := ioutil.ReadAll(c.Request.Body)
			if err != nil {
				logger.Error("读取请求体失败", zap.Error(err))
				c.Next()
				return
			}

			// 重置请求体
			c.Request.Body = ioutil.NopCloser(bytes.NewBuffer(body))

			// 解析JSON
			var data interface{}
			if err := json.Unmarshal(body, &data); err != nil {
				logger.Error("解析JSON失败", zap.Error(err))
				c.Next()
				return
			}

			// 过滤XSS
			filteredData := filterXss(data)

			// 重新编码JSON
			filteredBody, err := json.Marshal(filteredData)
			if err != nil {
				logger.Error("编码JSON失败", zap.Error(err))
				c.Next()
				return
			}

			// 重新设置请求体
			c.Request.Body = ioutil.NopCloser(bytes.NewBuffer(filteredBody))
			c.Request.ContentLength = int64(len(filteredBody))
		} else if strings.Contains(contentType, "application/x-www-form-urlencoded") {
			// 处理表单请求
			err := c.Request.ParseForm()
			if err != nil {
				logger.Error("解析表单失败", zap.Error(err))
				c.Next()
				return
			}

			// 过滤表单参数
			for key, values := range c.Request.PostForm {
				for i, value := range values {
					c.Request.PostForm[key][i] = html.EscapeString(value)
				}
			}

			// 过滤URL查询参数
			for key, values := range c.Request.URL.Query() {
				for i, value := range values {
					c.Request.URL.Query()[key][i] = html.EscapeString(value)
				}
			}
		}

		// 继续处理请求
		c.Next()
	}
}

// 判断是否需要过滤
func shouldFilter(path string) bool {
	// 排除静态资源和特定API
	excludePaths := []string{
		"/static/",
		"/public/",
		"/swagger/",
		"/api-docs/",
		"/favicon.ico",
	}

	for _, prefix := range excludePaths {
		if strings.HasPrefix(path, prefix) {
			return false
		}
	}

	return true
}

// 过滤XSS
func filterXss(data interface{}) interface{} {
	switch v := data.(type) {
	case string:
		return html.EscapeString(v)
	case []interface{}:
		for i, item := range v {
			v[i] = filterXss(item)
		}
		return v
	case map[string]interface{}:
		for key, value := range v {
			v[key] = filterXss(value)
		}
		return v
	default:
		return v
	}
}
