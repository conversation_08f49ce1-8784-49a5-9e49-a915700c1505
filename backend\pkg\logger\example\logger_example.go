package main

import (
	"context"

	"../../logger"
)

func main() {
	// 使用默认日志记录器
	logger.Info("这是一条默认日志记录器的信息")
	logger.Debug("这是一条默认日志记录器的调试信息")
	logger.Warn("这是一条默认日志记录器的警告信息")
	logger.Error("这是一条默认日志记录器的错误信息")

	// 使用带有键值对的日志
	logger.Infow("带有键值对的信息", "key1", "value1", "key2", 42)

	// 使用格式化日志
	logger.Infof("格式化日志: %s, %d", "字符串", 100)

	// 使用上下文日志
	ctx := context.Background()
	ctxLogger := logger.DefaultLogger.WithContext(ctx)
	ctxLogger.Info("这是一条带有上下文的日志")

	// 使用带有名称的日志记录器
	namedLogger := logger.DefaultLogger.WithName("MyComponent")
	namedLogger.Info("这是一条带有组件名称的日志")

	// 使用带有键值对的日志记录器
	kvLogger := logger.DefaultLogger.WithValues("userID", "123", "requestID", "abc-123")
	kvLogger.Info("这是一条带有固定键值对的日志")

	// 创建一个新的Zap日志记录器
	zapLogger, err := logger.GetLogger(logger.ZapLoggerType,
		logger.WithLevel(logger.DebugLevel),
		logger.WithFormat("console"),
		logger.WithDevelopment(true),
	)
	if err != nil {
		logger.Fatal("无法创建Zap日志记录器", err)
	}
	zapLogger.Debug("这是一条Zap日志记录器的调试信息")

	// 创建一个文件日志记录器
	fileLogger, err := logger.GetLogger(logger.FileLoggerType,
		logger.WithLevel(logger.InfoLevel),
		logger.WithFormat("json"),
		logger.WithOutputPaths("./logs/app.log"),
	)
	if err != nil {
		logger.Fatal("无法创建文件日志记录器", err)
	}
	fileLogger.Info("这是一条文件日志记录器的信息")

	// 创建一个每日轮转的日志记录器
	dailyLogger, err := logger.GetLogger(logger.DailyFileLoggerType,
		logger.WithLevel(logger.InfoLevel),
		logger.WithFormat("json"),
		logger.WithOutputPaths("./logs/myapp"),
	)
	if err != nil {
		logger.Fatal("无法创建每日轮转日志记录器", err)
	}
	dailyLogger.Info("这是一条每日轮转日志记录器的信息")

	// 创建一个命名的日志记录器
	namedFileLogger, err := logger.GetOrCreateLogger("api-server", logger.FileLoggerType,
		logger.WithLevel(logger.InfoLevel),
		logger.WithFormat("json"),
		logger.WithOutputPaths("./logs/api-server.log"),
	)
	if err != nil {
		logger.Fatal("无法创建命名的日志记录器", err)
	}
	namedFileLogger.Info("这是一条命名的日志记录器的信息")

	// 必须获取日志记录器（如果失败则panic）
	consoleLogger := logger.MustGetLogger(logger.ConsoleLoggerType)
	consoleLogger.Info("这是一条控制台日志记录器的信息")
}
