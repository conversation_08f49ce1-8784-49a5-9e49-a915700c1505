package system

import (
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/ruoyi/backend/internal/api/controller"
	"github.com/ruoyi/backend/internal/domain"
	"github.com/ruoyi/backend/internal/service"
	"go.uber.org/zap"
)

// SysDeptController 部门信息控制器
type SysDeptController struct {
	controller.BaseController
	deptService service.ISysDeptService
}

// NewSysDeptController 创建部门控制器
func NewSysDeptController(
	logger *zap.Logger,
	deptService service.ISysDeptService,
) *SysDeptController {
	return &SysDeptController{
		BaseController: controller.BaseController{
			Logger: logger,
		},
		deptService: deptService,
	}
}

// RegisterRoutes 注册路由
func (c *SysDeptController) RegisterRoutes(r *gin.RouterGroup) {
	r.GET("/list", c.List)
	r.GET("/list/exclude/:deptId", c.ExcludeChild)
	r.GET("/:deptId", c.GetInfo)
	r.POST("", c.Add)
	r.PUT("", c.Edit)
	r.DELETE("/:deptId", c.Remove)
}

// List 获取部门列表
func (c *SysDeptController) List(ctx *gin.Context) {
	// 构建查询条件
	dept := &domain.SysDept{}
	// 从请求中绑定参数
	// TODO: 实现参数绑定

	// 调用服务获取部门列表
	depts := c.deptService.SelectDeptList(*dept)
	c.SuccessWithData(ctx, depts)
}

// ExcludeChild 查询部门列表（排除节点）
func (c *SysDeptController) ExcludeChild(ctx *gin.Context) {
	deptIdStr := ctx.Param("deptId")
	deptId, err := strconv.ParseInt(deptIdStr, 10, 64)
	if err != nil {
		c.ErrorWithMessage(ctx, "部门ID格式错误")
		return
	}

	// 获取所有部门
	depts := c.deptService.SelectDeptList(domain.SysDept{})

	// 排除指定部门及其子部门
	result := make([]domain.SysDept, 0, len(depts))
	for _, d := range depts {
		// 排除自身
		if d.DeptId == deptId {
			continue
		}

		// 排除祖先包含该部门的部门
		ancestors := strings.Split(d.Ancestors, ",")
		isChild := false
		for _, ancestor := range ancestors {
			if ancestor == deptIdStr {
				isChild = true
				break
			}
		}

		if !isChild {
			result = append(result, d)
		}
	}

	c.SuccessWithData(ctx, result)
}

// GetInfo 根据部门编号获取详细信息
func (c *SysDeptController) GetInfo(ctx *gin.Context) {
	deptIdStr := ctx.Param("deptId")
	deptId, err := strconv.ParseInt(deptIdStr, 10, 64)
	if err != nil {
		c.ErrorWithMessage(ctx, "部门ID格式错误")
		return
	}

	// 检查数据权限
	c.deptService.CheckDeptDataScope(deptId)

	// 获取部门信息
	dept := c.deptService.SelectDeptById(deptId)
	c.SuccessWithData(ctx, dept)
}

// Add 新增部门
func (c *SysDeptController) Add(ctx *gin.Context) {
	var dept domain.SysDept
	if err := ctx.ShouldBindJSON(&dept); err != nil {
		c.ErrorWithMessage(ctx, "参数错误")
		return
	}

	// 校验部门名称唯一性
	if !c.deptService.CheckDeptNameUnique(dept) {
		c.ErrorWithMessage(ctx, "新增部门'"+dept.DeptName+"'失败，部门名称已存在")
		return
	}

	// 设置创建者
	dept.CreateBy = c.GetUsername(ctx)

	// 新增部门
	rows := c.deptService.InsertDept(dept)
	c.ToAjax(ctx, rows)
}

// Edit 修改部门
func (c *SysDeptController) Edit(ctx *gin.Context) {
	var dept domain.SysDept
	if err := ctx.ShouldBindJSON(&dept); err != nil {
		c.ErrorWithMessage(ctx, "参数错误")
		return
	}

	deptId := dept.DeptId
	// 检查数据权限
	c.deptService.CheckDeptDataScope(deptId)

	// 校验部门名称唯一性
	if !c.deptService.CheckDeptNameUnique(dept) {
		c.ErrorWithMessage(ctx, "修改部门'"+dept.DeptName+"'失败，部门名称已存在")
		return
	} else if dept.ParentId == deptId {
		c.ErrorWithMessage(ctx, "修改部门'"+dept.DeptName+"'失败，上级部门不能是自己")
		return
	} else if dept.Status == "1" && c.deptService.SelectNormalChildrenDeptById(deptId) > 0 {
		c.ErrorWithMessage(ctx, "该部门包含未停用的子部门！")
		return
	}

	// 设置更新者
	dept.UpdateBy = c.GetUsername(ctx)

	// 更新部门
	rows := c.deptService.UpdateDept(dept)
	c.ToAjax(ctx, rows)
}

// Remove 删除部门
func (c *SysDeptController) Remove(ctx *gin.Context) {
	deptIdStr := ctx.Param("deptId")
	deptId, err := strconv.ParseInt(deptIdStr, 10, 64)
	if err != nil {
		c.ErrorWithMessage(ctx, "部门ID格式错误")
		return
	}

	// 检查是否存在子部门
	if c.deptService.HasChildByDeptId(deptId) {
		c.Warn(ctx, "存在下级部门,不允许删除")
		return
	}

	// 检查部门是否存在用户
	if c.deptService.CheckDeptExistUser(deptId) {
		c.Warn(ctx, "部门存在用户,不允许删除")
		return
	}

	// 检查数据权限
	c.deptService.CheckDeptDataScope(deptId)

	// 删除部门
	rows := c.deptService.DeleteDeptById(deptId)
	c.ToAjax(ctx, rows)
}
