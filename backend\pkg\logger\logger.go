package logger

import (
	"os"
	"path/filepath"
	"time"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// 自定义日志字段
const (
	TraceIDKey = "trace_id"
	UserIDKey  = "user_id"
	IPKey      = "ip"
	URIKey     = "uri"
	MethodKey  = "method"
	StatusKey  = "status"
)

// Config 日志配置
type Config struct {
	// 日志级别
	Level string

	// 日志路径
	Path string

	// 日志文件名前缀
	Prefix string

	// 日志格式 (json 或 console)
	Format string

	// 是否输出到控制台
	Console bool

	// 是否输出调用者信息
	Caller bool

	// 是否输出堆栈信息
	Stacktrace bool

	// 是否显示颜色
	Color bool

	// 日志切割
	// 单文件最大大小，单位MB
	MaxSize int

	// 最大历史文件保留数量
	MaxBackups int

	// 最长保留天数
	MaxAge int

	// 是否压缩
	Compress bool
}

// NewStdLogger 创建标准日志
func NewStdLogger() *zap.Logger {
	config := zap.NewProductionConfig()
	config.EncoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
	logger, _ := config.Build()
	return logger
}

// NewDevLogger 创建开发环境日志
func NewDevLogger() *zap.Logger {
	config := zap.NewDevelopmentConfig()
	config.EncoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
	logger, _ := config.Build()
	return logger
}

// NewFileLogger 创建文件日志
func NewFileLogger(cfg *Config) (*zap.Logger, error) {
	// 确保日志目录存在
	if err := os.MkdirAll(cfg.Path, 0755); err != nil {
		return nil, err
	}

	// 设置日志级别
	level := zapcore.InfoLevel
	if err := level.UnmarshalText([]byte(cfg.Level)); err != nil {
		return nil, err
	}

	// 创建编码器
	encoderConfig := zapcore.EncoderConfig{
		TimeKey:        "time",
		LevelKey:       "level",
		NameKey:        "logger",
		CallerKey:      "caller",
		FunctionKey:    zapcore.OmitKey,
		MessageKey:     "msg",
		StacktraceKey:  "stacktrace",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    zapcore.CapitalLevelEncoder,
		EncodeTime:     zapcore.ISO8601TimeEncoder,
		EncodeDuration: zapcore.StringDurationEncoder,
		EncodeCaller:   zapcore.ShortCallerEncoder,
	}

	if cfg.Color {
		encoderConfig.EncodeLevel = zapcore.CapitalColorLevelEncoder
	}

	// 设置日志文件路径
	now := time.Now()
	filename := filepath.Join(
		cfg.Path,
		cfg.Prefix+now.Format("2006-01-02")+".log",
	)

	// 创建日志文件
	file, err := os.OpenFile(filename, os.O_CREATE|os.O_APPEND|os.O_WRONLY, 0644)
	if err != nil {
		return nil, err
	}

	// 创建核心
	cores := []zapcore.Core{}

	// 文件输出
	var encoder zapcore.Encoder
	if cfg.Format == "json" {
		encoder = zapcore.NewJSONEncoder(encoderConfig)
	} else {
		encoder = zapcore.NewConsoleEncoder(encoderConfig)
	}
	fileCore := zapcore.NewCore(
		encoder,
		zapcore.AddSync(file),
		zap.NewAtomicLevelAt(level),
	)
	cores = append(cores, fileCore)

	// 控制台输出
	if cfg.Console {
		consoleCore := zapcore.NewCore(
			zapcore.NewConsoleEncoder(encoderConfig),
			zapcore.AddSync(os.Stdout),
			zap.NewAtomicLevelAt(level),
		)
		cores = append(cores, consoleCore)
	}

	// 合并核心
	core := zapcore.NewTee(cores...)

	// 创建日志实例
	logger := zap.New(core)

	// 添加调用者信息
	if cfg.Caller {
		logger = logger.WithOptions(zap.AddCaller(), zap.AddCallerSkip(1))
	}

	// 添加堆栈跟踪
	if cfg.Stacktrace {
		logger = logger.WithOptions(zap.AddStacktrace(zapcore.ErrorLevel))
	}

	return logger, nil
}

// Info 记录信息
func Info(msg string, fields ...interface{}) {
	zap.L().Sugar().Infow(msg, fields...)
}

// Debug 记录调试信息
func Debug(msg string, fields ...interface{}) {
	zap.L().Sugar().Debugw(msg, fields...)
}

// Warn 记录警告信息
func Warn(msg string, fields ...interface{}) {
	zap.L().Sugar().Warnw(msg, fields...)
}

// Error 记录错误信息
func Error(msg string, fields ...interface{}) {
	zap.L().Sugar().Errorw(msg, fields...)
}

// Fatal 记录致命错误并退出
func Fatal(msg string, fields ...interface{}) {
	zap.L().Sugar().Fatalw(msg, fields...)
}

// Panic 记录Panic信息并引发Panic
func Panic(msg string, fields ...interface{}) {
	zap.L().Sugar().Panicw(msg, fields...)
}

// With 创建带有指定字段的Logger
func With(fields ...interface{}) *zap.SugaredLogger {
	return zap.L().Sugar().With(fields...)
}
