package config

import (
	"fmt"
	"time"
)

// DBConfig 数据库配置
type DBConfig struct {
	// 类型
	Type string `mapstructure:"type" json:"type"`

	// 地址
	Host string `mapstructure:"host" json:"host"`

	// 端口
	Port int `mapstructure:"port" json:"port"`

	// 用户名
	Username string `mapstructure:"username" json:"username"`

	// 密码
	Password string `mapstructure:"password" json:"password"`

	// 数据库名
	Database string `mapstructure:"database" json:"database"`

	// 参数
	Params string `mapstructure:"params" json:"params"`

	// 最大空闲连接数
	MaxIdleConns int `mapstructure:"max_idle_conns" json:"max_idle_conns"`

	// 最大打开连接数
	MaxOpenConns int `mapstructure:"max_open_conns" json:"max_open_conns"`

	// 连接最大生命周期（小时）
	ConnMaxLifetime int `mapstructure:"conn_max_lifetime" json:"conn_max_lifetime"`
}

// GetDSN 获取数据源连接字符串
func (c *DBConfig) GetDSN() string {
	switch c.Type {
	case "mysql":
		return fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?%s",
			c.Username, c.Password, c.Host, c.Port, c.Database, c.Params)
	case "postgres":
		return fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s %s",
			c.Host, c.Port, c.Username, c.Password, c.Database, c.Params)
	case "sqlserver":
		return fmt.Sprintf("sqlserver://%s:%s@%s:%d?database=%s&%s",
			c.Username, c.Password, c.Host, c.Port, c.Database, c.Params)
	case "sqlite3":
		return c.Database
	default:
		return ""
	}
}

// GetConnMaxLifetime 获取连接最大生命周期
func (c *DBConfig) GetConnMaxLifetime() time.Duration {
	return time.Duration(c.ConnMaxLifetime) * time.Hour
}

// IsSQLServer 是否是SQL Server
func (c *DBConfig) IsSQLServer() bool {
	return c.Type == "sqlserver"
}

// IsMySQL 是否是MySQL
func (c *DBConfig) IsMySQL() bool {
	return c.Type == "mysql"
}

// IsPostgreSQL 是否是PostgreSQL
func (c *DBConfig) IsPostgreSQL() bool {
	return c.Type == "postgres"
}

// IsSQLite 是否是SQLite
func (c *DBConfig) IsSQLite() bool {
	return c.Type == "sqlite3"
}
